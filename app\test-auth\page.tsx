"use client";

import React from "react";
import { useAuthState } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>cle, XCircle, Loader2 } from "lucide-react";

/**
 * Authentication System Test Page
 * Tests the rebuilt authentication system components
 */
export default function TestAuthPage() {
  const {
    user,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
    error,
  } = useAuthState();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading authentication state...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Authentication System Test</h1>
        <p className="text-muted-foreground">
          Testing the rebuilt CateringHub authentication system
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isAuthenticated ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Authenticated:</span>
              <Badge variant={isAuthenticated ? "default" : "destructive"}>
                {isAuthenticated ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Loading:</span>
              <Badge variant={isLoading ? "secondary" : "outline"}>
                {isLoading ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Error:</span>
              <Badge variant={error ? "destructive" : "outline"}>
                {error ? "Yes" : "No"}
              </Badge>
            </div>
            {error && (
              <p className="text-sm text-red-600 mt-2">{error.message}</p>
            )}
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>User ID:</span>
              <span className="text-sm font-mono">
                {user?.id ? `${user.id.substring(0, 8)}...` : "N/A"}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Email:</span>
              <span className="text-sm">{user?.email || "N/A"}</span>
            </div>
            <div className="flex justify-between">
              <span>Full Name:</span>
              <span className="text-sm">{profile?.full_name || "N/A"}</span>
            </div>
            <div className="flex justify-between">
              <span>Username:</span>
              <span className="text-sm">{profile?.username || "N/A"}</span>
            </div>
          </CardContent>
        </Card>

        {/* Role Information */}
        <Card>
          <CardHeader>
            <CardTitle>Role Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Primary Role:</span>
              <Badge variant="secondary">
                {userRole?.role || "user"}
              </Badge>
            </div>
            {userRole?.provider_role && (
              <div className="flex justify-between">
                <span>Provider Role:</span>
                <Badge variant="outline">
                  {userRole.provider_role}
                </Badge>
              </div>
            )}
            <div className="flex justify-between">
              <span>Is Admin:</span>
              <Badge variant={isAdmin ? "default" : "outline"}>
                {isAdmin ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is Provider:</span>
              <Badge variant={isProvider ? "default" : "outline"}>
                {isProvider ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is Provider Owner:</span>
              <Badge variant={isProviderOwner ? "default" : "outline"}>
                {isProviderOwner ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is Provider Staff:</span>
              <Badge variant={isProviderStaff ? "default" : "outline"}>
                {isProviderStaff ? "Yes" : "No"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Hooks Working:</span>
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Yes
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>TanStack Query:</span>
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Integrated
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Supabase SSR:</span>
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Role System:</span>
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Simplified
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {!isAuthenticated && (
              <>
                <Button asChild>
                  <a href="/login">Test Login</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/signup">Test Signup</a>
                </Button>
              </>
            )}
            {isAuthenticated && (
              <>
                <Button asChild>
                  <a href="/dashboard">Go to Dashboard</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/profile">View Profile</a>
                </Button>
                {isAdmin && (
                  <Button variant="secondary" asChild>
                    <a href="/users">Admin: Users</a>
                  </Button>
                )}
                {isProvider && (
                  <Button variant="secondary" asChild>
                    <a href="/bookings">Provider: Bookings</a>
                  </Button>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Debug Information (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-4 rounded overflow-auto">
              {JSON.stringify(
                {
                  user: user ? { id: user.id, email: user.email } : null,
                  profile,
                  userRole,
                  flags: {
                    isAuthenticated,
                    isAdmin,
                    isProvider,
                    isProviderOwner,
                    isProviderStaff,
                  },
                },
                null,
                2
              )}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import type { AppRole, ProviderRoleType } from '@/types'

/* ================================================================== */
/* AUTHENTICATION ACTIONS - REBUILT FROM SCRATCH                    */
/* Following official Supabase patterns with proper error handling   */
/* ================================================================== */

/**
 * Sign in with email and password
 * Follows official Supabase authentication patterns
 */
export async function login(formData: FormData) {
  try {
    const supabase = await createClient()

    const email = formData.get('email') as string
    const password = formData.get('password') as string

    if (!email || !password) {
      return { error: 'Email and password are required' }
    }

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      return { error: error.message }
    }

    revalidatePath('/', 'layout')
    redirect('/dashboard')
  } catch (error) {
    console.error('Login error:', error)
    return { error: 'An unexpected error occurred during login' }
  }
}

/**
 * Sign in with Google OAuth
 * Follows official Supabase OAuth patterns
 */
export async function signInWithGoogle() {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        scopes: 'profile email',
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { url: data.url }
  } catch (error) {
    console.error('Google sign-in error:', error)
    return { error: 'An unexpected error occurred during Google sign-in' }
  }
}

/**
 * Sign in with Facebook OAuth
 * Follows official Supabase OAuth patterns
 */
export async function signInWithFacebook() {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'facebook',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        scopes: 'email',
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { url: data.url }
  } catch (error) {
    console.error('Facebook sign-in error:', error)
    return { error: 'An unexpected error occurred during Facebook sign-in' }
  }
}

/**
 * Sign up with email and password
 * Assigns admin role by default as requested
 */
export async function signup(formData: FormData) {
  try {
    const supabase = await createClient()

    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const fullName = formData.get('full_name') as string

    if (!email || !password || !fullName) {
      return { error: 'Email, password, and full name are required' }
    }

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    })

    if (error) {
      return { error: error.message }
    }

    return { success: 'Check your email for the confirmation link.' }
  } catch (error) {
    console.error('Signup error:', error)
    return { error: 'An unexpected error occurred during signup' }
  }
}

/**
 * Sign out the current user
 * Clears session and redirects to home page
 */
export async function signout() {
  try {
    const supabase = await createClient()
    await supabase.auth.signOut()
    revalidatePath('/', 'layout')
    redirect('/')
  } catch (error) {
    console.error('Signout error:', error)
    // Still redirect even if there's an error
    redirect('/')
  }
}

/**
 * Get user role from database (not JWT)
 * Following simplified role-based access control
 */
export async function getUserRole(): Promise<{
  role: AppRole;
  provider_role: ProviderRoleType | null;
} | null> {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return null
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select('role, provider_role')
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No role found - return default
        return { role: 'user', provider_role: null }
      }
      console.error('Error fetching user role:', error.message)
      return null
    }

    return {
      role: data.role,
      provider_role: data.provider_role,
    }
  } catch (error) {
    console.error('Unexpected error in getUserRole:', error)
    return null
  }
}
/**
 * Role-based helper functions
 * Using simplified database-based role checking
 */
export async function isAdmin(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'admin'
}

export async function isProvider(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider'
}

export async function isProviderOwner(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider' && userRoleData?.provider_role === 'owner'
}

export async function isProviderStaff(): Promise<boolean> {
  const userRoleData = await getUserRole()
  return userRoleData?.role === 'catering_provider' && userRoleData?.provider_role === 'staff'
}

/**
 * Role management functions for admin users
 * Following simplified role-based access control
 */
export async function assignUserRole(userId: string, role: AppRole, providerRole?: ProviderRoleType) {
  try {
    const supabase = await createClient()

    // Check if current user is admin
    const isCurrentUserAdmin = await isAdmin()
    if (!isCurrentUserAdmin) {
      return { error: 'Only admins can assign roles' }
    }

    // Insert or update user role
    const { error } = await supabase
      .from('user_roles')
      .upsert({
        user_id: userId,
        role,
        provider_role: providerRole || null,
        updated_at: new Date().toISOString(),
      })

    if (error) {
      console.error('Error assigning user role:', error.message)
      return { error: error.message }
    }

    revalidatePath('/', 'layout')
    return { success: 'Role assigned successfully' }
  } catch (error) {
    console.error('Unexpected error in assignUserRole:', error)
    return { error: 'An unexpected error occurred' }
  }
}

/**
 * Remove user role (admin only)
 */
export async function removeUserRole(userId: string, role: AppRole) {
  try {
    const supabase = await createClient()

    // Check if current user is admin
    const isCurrentUserAdmin = await isAdmin()
    if (!isCurrentUserAdmin) {
      return { error: 'Only admins can remove roles' }
    }

    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId)
      .eq('role', role)

    if (error) {
      console.error('Error removing user role:', error.message)
      return { error: error.message }
    }

    revalidatePath('/', 'layout')
    return { success: 'Role removed successfully' }
  } catch (error) {
    console.error('Unexpected error in removeUserRole:', error)
    return { error: 'An unexpected error occurred' }
  }
}

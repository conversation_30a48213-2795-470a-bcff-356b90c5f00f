-- ============================================================================
-- CateringHub Authentication Schema - Complete Rebuild
-- Following official Supabase patterns with performance optimizations
-- ============================================================================

-- Drop existing objects for clean rebuild
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.custom_access_token_hook(jsonb) CASCADE;
DROP FUNCTION IF EXISTS public.get_user_role() CASCADE;
DROP FUNCTION IF EXISTS public.is_provider_owner() CASCADE;
DROP FUNCTION IF EXISTS public.is_provider() CASCADE;
DROP FUNCTION IF EXISTS public.is_admin() CASCADE;
DROP TABLE IF EXISTS public.user_roles CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;
DROP TYPE IF EXISTS public.provider_role_type CASCADE;
DROP TYPE IF EXISTS public.app_role CASCADE;

-- ============================================================================
-- SIMPLIFIED ROLE TYPES
-- ============================================================================
CREATE TYPE public.app_role AS ENUM ('user', 'admin', 'catering_provider');
CREATE TYPE public.provider_role_type AS ENUM ('owner', 'staff');

-- ============================================================================
-- PROFILES TABLE
-- ============================================================================
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  username TEXT UNIQUE,
  bio TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Performance indexes
CREATE INDEX idx_profiles_username ON public.profiles(username);
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at);

-- ============================================================================
-- USER ROLES TABLE
-- ============================================================================
CREATE TABLE public.user_roles (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role app_role NOT NULL DEFAULT 'admin',
  provider_role provider_role_type,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (user_id, role)
);

-- Performance indexes
CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role ON public.user_roles(role);
CREATE INDEX idx_user_roles_provider_role ON public.user_roles(provider_role) WHERE provider_role IS NOT NULL;
CREATE INDEX idx_user_roles_user_id_role ON public.user_roles(user_id, role);

-- ============================================================================
-- ROW LEVEL SECURITY
-- ============================================================================
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- OPTIMIZED RLS POLICIES FOR PROFILES
-- ============================================================================
CREATE POLICY "profiles_unified_select_policy"
  ON public.profiles FOR SELECT
  TO authenticated
  USING (
    id = (SELECT auth.uid()) OR
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "profiles_insert_policy"
  ON public.profiles FOR INSERT
  TO authenticated
  WITH CHECK (id = (SELECT auth.uid()));

CREATE POLICY "profiles_update_policy"
  ON public.profiles FOR UPDATE
  TO authenticated
  USING (
    id = (SELECT auth.uid()) OR
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

CREATE POLICY "profiles_delete_policy"
  ON public.profiles FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- ============================================================================
-- OPTIMIZED RLS POLICIES FOR USER_ROLES
-- ============================================================================
CREATE POLICY "user_roles_unified_select_policy"
  ON public.user_roles FOR SELECT
  TO authenticated
  USING (
    user_id = (SELECT auth.uid()) OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.user_id = (SELECT auth.uid()) AND ur.role = 'admin'
    )
  );

CREATE POLICY "user_roles_admin_write_policy"
  ON public.user_roles FOR INSERT, UPDATE, DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
    )
  );

-- Special policy for auth hook
CREATE POLICY "auth_hook_can_read_user_roles"
  ON public.user_roles FOR SELECT
  TO service_role
  USING (true);

-- ============================================================================
-- SECURITY-OPTIMIZED FUNCTIONS
-- ============================================================================
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = (SELECT auth.uid()) AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = (SELECT auth.uid()) AND role = 'catering_provider'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider_owner()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = (SELECT auth.uid()) 
    AND role = 'catering_provider' 
    AND provider_role = 'owner'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.get_user_role()
RETURNS public.app_role AS $$
DECLARE
  user_role public.app_role;
BEGIN
  SELECT role INTO user_role
  FROM public.user_roles
  WHERE user_id = (SELECT auth.uid())
  LIMIT 1;
  
  RETURN COALESCE(user_role, 'user'::public.app_role);
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- ============================================================================
-- USER REGISTRATION TRIGGER
-- ============================================================================
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  avatar_url TEXT;
BEGIN
  -- Extract avatar URL from OAuth providers
  IF NEW.raw_user_meta_data->>'avatar_url' IS NOT NULL THEN
    avatar_url := NEW.raw_user_meta_data->>'avatar_url';
  ELSIF NEW.identities IS NOT NULL AND jsonb_array_length(NEW.identities) > 0 THEN
    IF NEW.identities[0]->>'provider' = 'google' THEN
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    ELSIF NEW.identities[0]->>'provider' = 'facebook' THEN
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    END IF;
  ELSE
    avatar_url := NULL;
  END IF;

  -- Create profile
  INSERT INTO public.profiles (id, full_name, avatar_url, updated_at)
  VALUES (
    NEW.id,
    COALESCE(
      NEW.raw_user_meta_data->>'full_name', 
      NEW.identities[0]->'identity_data'->>'full_name', 
      NEW.identities[0]->'identity_data'->>'name'
    ),
    avatar_url,
    NOW()
  );

  -- Assign default admin role as requested
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'admin');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = '';

-- Create trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- JWT CLAIMS HOOK
-- ============================================================================
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb AS $$
DECLARE
  claims jsonb;
  user_role public.app_role;
  provider_role_val public.provider_role_type;
BEGIN
  -- Get user role and provider role
  SELECT ur.role, ur.provider_role INTO user_role, provider_role_val
  FROM public.user_roles ur
  WHERE ur.user_id = (event->>'user_id')::uuid
  LIMIT 1;

  -- Set default claims
  claims := event->'claims';
  
  -- Add role to claims
  claims := jsonb_set(claims, '{user_role}', to_jsonb(COALESCE(user_role, 'user'::public.app_role)));
  
  -- Add provider role if exists
  IF provider_role_val IS NOT NULL THEN
    claims := jsonb_set(claims, '{provider_role}', to_jsonb(provider_role_val));
  END IF;

  -- Return the modified event
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- ============================================================================
-- PERMISSIONS
-- ============================================================================
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT ALL ON TABLE public.user_roles TO supabase_auth_admin;
GRANT ALL ON TABLE public.profiles TO supabase_auth_admin;

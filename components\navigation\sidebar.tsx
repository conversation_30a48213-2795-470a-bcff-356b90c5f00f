"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useAuthState } from "@/hooks/use-auth";
import {
  Home,
  Users,
  Calendar,
  MessageSquare,
  Star,
  BarChart3,
  Settings,
  ChefHat,
  Building2,
  UserPlus,
  Menu,
  X,
  LogOut,
  User,
} from "lucide-react";

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  roles?: string[]; // Roles that can see this item
  providerRoles?: string[]; // Provider sub-roles that can see this item
}

const navigationItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
    roles: ["admin", "user", "catering_provider"],
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
    roles: ["admin"],
  },
  {
    title: "Services",
    href: "/services",
    icon: ChefHat,
    roles: ["catering_provider"],
    providerRoles: ["owner"],
  },
  {
    title: "Bookings",
    href: "/bookings",
    icon: Calendar,
    roles: ["catering_provider"],
  },
  {
    title: "Messages",
    href: "/messages",
    icon: MessageSquare,
    roles: ["catering_provider"],
  },
  {
    title: "Reviews",
    href: "/reviews",
    icon: Star,
    roles: ["catering_provider"],
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
    roles: ["catering_provider"],
    providerRoles: ["owner"],
  },
  {
    title: "Staff",
    href: "/staff",
    icon: UserPlus,
    roles: ["catering_provider"],
    providerRoles: ["owner"],
  },
  {
    title: "Profile",
    href: "/profile",
    icon: User,
    roles: ["admin", "user", "catering_provider"],
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
    roles: ["admin", "user", "catering_provider"],
  },
];

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const {
    user,
    userRole,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
  } = useAuthState();

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter((item) => {
    if (!item.roles) return true;

    const userRoleValue = userRole?.role || "user";
    const providerRoleValue = userRole?.provider_role;

    // Check if user has required role
    if (!item.roles.includes(userRoleValue)) return false;

    // If item has provider role requirements, check them
    if (item.providerRoles && userRoleValue === "catering_provider") {
      return item.providerRoles.includes(providerRoleValue || "");
    }

    return true;
  });

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div
      className={cn(
        "flex h-full flex-col border-r bg-background transition-all duration-300",
        isCollapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <Building2 className="h-6 w-6 text-primary" />
            <span className="font-semibold text-lg">CateringHub</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? (
            <Menu className="h-4 w-4" />
          ) : (
            <X className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* User Info */}
      {!isCollapsed && user && (
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
              <User className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {user.email?.split("@")[0] || "User"}
              </p>
              <div className="flex items-center space-x-1">
                <Badge variant="secondary" className="text-xs">
                  {userRole?.role || "user"}
                </Badge>
                {userRole?.provider_role && (
                  <Badge variant="outline" className="text-xs">
                    {userRole.provider_role}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start h-10",
                    isCollapsed ? "px-2" : "px-3",
                    isActive && "bg-secondary"
                  )}
                >
                  <Icon className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1 text-left">{item.title}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </>
                  )}
                </Button>
              </Link>
            );
          })}
        </nav>
      </ScrollArea>

      {/* Provider CTA */}
      {!isProvider && !isCollapsed && (
        <div className="p-4 border-t">
          <Link href="/become-provider">
            <Button className="w-full" size="sm">
              <ChefHat className="h-4 w-4 mr-2" />
              Become a Provider
            </Button>
          </Link>
        </div>
      )}

      {/* Footer */}
      <div className="p-4 border-t">
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start h-10 text-muted-foreground hover:text-foreground",
            isCollapsed ? "px-2" : "px-3"
          )}
          onClick={() => {
            // Handle sign out
            window.location.href = "/api/auth/signout";
          }}
        >
          <LogOut className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
          {!isCollapsed && "Sign Out"}
        </Button>
      </div>
    </div>
  );
}

"use client";

import React, { ReactNode } from "react";
import { useAuthState } from "@/hooks/use-auth";
import { getAvatarUrl } from "@/lib/utils/avatar";

interface AuthProviderProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Seamless authentication provider for CateringHub
 * Rebuilt from scratch following official Supabase patterns
 * Provides transparent background authentication without debug components
 * Integrates with TanStack Query for optimal performance
 */
export function AuthProvider({ children, fallback }: AuthProviderProps) {
  const {
    user,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
    error,
  } = useAuthState();

  // Show loading fallback while authentication is being determined
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )
    );
  }

  // Handle authentication errors gracefully without breaking the app
  if (error) {
    // Only log in development to avoid console spam in production
    if (process.env.NODE_ENV === "development") {
      console.warn("Authentication error:", error.message);
    }
    // Continue rendering - individual components handle auth requirements
  }

  // Prepare clean user data for components
  const userData = user
    ? {
        id: user.id,
        name: profile?.full_name || user.email?.split("@")[0] || "User",
        email: user.email || "",
        avatar: getAvatarUrl(
          profile?.avatar_url,
          profile?.full_name || user.email?.split("@")[0] || "User"
        ),
        role: userRole?.role || "user",
        providerRole: userRole?.provider_role || null,
        isAdmin,
        isProvider,
        isProviderOwner,
        isProviderStaff,
      }
    : null;

  // Create auth context value
  const authContext = {
    user: userData,
    profile,
    userRole,
    isLoading,
    isAuthenticated,
    isAdmin,
    isProvider,
    isProviderOwner,
    isProviderStaff,
  };

  // Clone children with auth context
  return (
    <>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(
            child as React.ReactElement<{ auth?: typeof authContext }>,
            {
              auth: authContext,
            }
          );
        }
        return child;
      })}
    </>
  );
}

// Legacy alias for backward compatibility
export const UserProvider = AuthProvider;

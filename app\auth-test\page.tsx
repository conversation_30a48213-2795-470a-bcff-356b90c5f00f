"use client";

import { AuthTest } from "@/components/auth-test";
import { RoleGuard } from "@/components/permission-guard";
import { Typography } from "@/components/ui/typography";

/**
 * Authentication Test Page
 * This page is used to test and validate the rebuilt authentication system
 * It should only be accessible to authenticated users
 */
export default function AuthTestPage() {
  return (
    <RoleGuard roles={["user", "admin", "catering_provider"]} requireAuth={true}>
      <div className="container mx-auto py-8">
        <Typography variant="h1" className="mb-6">
          Authentication System Test
        </Typography>
        <Typography variant="lead" className="mb-8">
          This page tests all authentication hooks and displays the current authentication state.
        </Typography>
        <AuthTest />
      </div>
    </RoleGuard>
  );
}

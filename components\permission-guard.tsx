"use client";

import React, { ReactNode } from "react";
import {
  useIsAdmin,
  useIsProvider,
  useIsProviderOwner,
  useUser,
} from "@/hooks/use-auth";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Lock, LogIn } from "lucide-react";
import { useRouter } from "next/navigation";
import type { AppRole } from "@/types";

interface RoleGuardProps {
  roles: AppRole[];
  fallback?: ReactNode;
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  requireOwner?: boolean; // For catering_provider role, require owner sub-role
}

export function RoleGuard({
  roles,
  fallback,
  children,
  requireAuth = true,
  redirectTo = "/login",
  requireOwner = false,
}: RoleGuardProps) {
  const { data: user, isLoading: isUserLoading } = useUser();
  const { isAdmin, isLoading: isAdminLoading } = useIsAdmin();
  const { isProvider, isLoading: isProviderLoading } = useIsProvider();
  const { isProviderOwner, isLoading: isProviderOwnerLoading } =
    useIsProviderOwner();
  const router = useRouter();

  // Show loading state while checking authentication and roles
  const isLoading =
    isUserLoading ||
    isAdminLoading ||
    isProviderLoading ||
    isProviderOwnerLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check if user is authenticated (if required)
  if (requireAuth && !user) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Alert className="max-w-md">
            <LogIn className="h-4 w-4" />
            <AlertTitle>Authentication Required</AlertTitle>
            <AlertDescription className="space-y-3">
              <p>You need to be logged in to access this content.</p>
              <Button
                onClick={() => router.push(redirectTo)}
                className="w-full"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Sign In
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )
    );
  }

  // Check if user has any of the required roles
  const hasRequiredRole = roles.some((role) => {
    switch (role) {
      case "admin":
        return isAdmin;
      case "catering_provider":
        // If requireOwner is true, check for provider owner specifically
        return requireOwner ? isProviderOwner : isProvider;
      case "user":
        return true; // All authenticated users have user role access
      default:
        return false;
    }
  });

  if (!hasRequiredRole) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Alert variant="destructive" className="max-w-md">
            <Lock className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription className="space-y-3">
              <p>
                You don&apos;t have the required role to access this content.
              </p>
              <p className="text-sm text-muted-foreground">
                Required roles:{" "}
                <code className="bg-muted px-1 rounded">
                  {roles.join(", ")}
                </code>
                {requireOwner && " (owner level required)"}
              </p>
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )
    );
  }

  return <>{children}</>;
}

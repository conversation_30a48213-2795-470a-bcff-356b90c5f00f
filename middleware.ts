import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from '@/types/supabase'

// ==================================================================
// ROUTE CONFIGURATION FOR SIMPLIFIED ROLE SYSTEM
// ==================================================================

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/login',
  '/signup',
  '/',
  '/auth/error',
  '/auth/reset-password',
  '/auth/callback',
  '/auth/confirm',
  '/become-provider'
]

// Admin-only routes
const ADMIN_ROUTES = [
  '/users',
  '/admin'
]

// Provider-only routes
const PROVIDER_ROUTES = [
  '/services',
  '/bookings',
  '/messages',
  '/reviews',
  '/analytics',
  '/staff'
]

// Provider owner-only routes
const PROVIDER_OWNER_ROUTES = [
  '/services',
  '/analytics',
  '/staff'
]

export async function middleware(request: NextRequest) {
  try {
    // Create a Supabase client configured to use cookies
    let supabaseResponse = NextResponse.next({
      request,
    })

    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))
            supabaseResponse = NextResponse.next({
              request,
            })
            cookiesToSet.forEach(({ name, value, options }) =>
              supabaseResponse.cookies.set(name, value, options)
            )
          },
        },
      }
    )

    // IMPORTANT: Avoid writing any logic between createServerClient and
    // supabase.auth.getUser(). A simple mistake could make it very hard to debug
    // issues with users being randomly logged out.

    const {
      data: { user },
    } = await supabase.auth.getUser()

    const { pathname } = request.nextUrl
    const isPublicRoute = PUBLIC_ROUTES.includes(pathname)
    const isAdminRoute = ADMIN_ROUTES.some(route => pathname.startsWith(route))
    const isProviderRoute = PROVIDER_ROUTES.some(route => pathname.startsWith(route))
    const isProviderOwnerRoute = PROVIDER_OWNER_ROUTES.some(route => pathname.startsWith(route))

    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (user && (pathname === '/login' || pathname === '/signup')) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // If user is authenticated and on root path, redirect to dashboard
    if (user && pathname === '/') {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // If user is not authenticated and trying to access protected routes, redirect to login
    if (!user && !isPublicRoute) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // Role-based route protection
    if (user && (isAdminRoute || isProviderRoute)) {
      try {
        // Get user role from database
        const { data: userRole, error } = await supabase
          .from('user_roles')
          .select('role, provider_role')
          .eq('user_id', user.id)
          .single()

        if (error || !userRole) {
          // If no role found, redirect to dashboard
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }

        // Check admin routes
        if (isAdminRoute && userRole.role !== 'admin') {
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }

        // Check provider routes
        if (isProviderRoute && userRole.role !== 'catering_provider') {
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }

        // Check provider owner routes
        if (isProviderOwnerRoute &&
            (userRole.role !== 'catering_provider' || userRole.provider_role !== 'owner')) {
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }
      } catch (error) {
        console.error('Error checking user role in middleware:', error)
        // On error, allow access but log the issue
      }
    }

    return supabaseResponse
  } catch (error) {
    console.error('Middleware error:', error)
    // On error, continue with the request
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - sw.js (service worker file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|sw.js|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

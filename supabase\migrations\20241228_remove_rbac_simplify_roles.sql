-- Migration: Remove RBAC System and Implement Simplified Role-Based Authentication
-- This migration removes the complex RBAC system and replaces it with simple role-based authentication
-- Date: 2024-12-28

-- Step 1: Backup existing user role data before making changes
-- This ensures we can preserve user roles during the transition
CREATE TEMP TABLE user_roles_backup AS 
SELECT user_id, role, provider_role, created_at 
FROM public.user_roles;

-- Step 2: Drop all RBAC-related RLS policies
-- These policies use the authorize() function which we'll be removing

-- Drop profiles policies that use authorize()
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- Drop user_roles policies that use authorize()
DROP POLICY IF EXISTS "user_roles_select_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_insert_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_update_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_delete_policy" ON public.user_roles;

-- Drop role_permissions policies
DROP POLICY IF EXISTS "All authenticated users can read role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_insert_policy" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_update_policy" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_delete_policy" ON public.role_permissions;

-- Drop provider_role_permissions policies
DROP POLICY IF EXISTS "All authenticated users can read provider permissions" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_insert_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_update_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_delete_policy" ON public.provider_role_permissions;

-- Step 3: Drop RBAC-related functions
DROP FUNCTION IF EXISTS public.authorize(app_permission);
DROP FUNCTION IF EXISTS public.has_permission(app_permission);

-- Step 4: Drop RBAC-related tables
-- These tables store the granular permission mappings we no longer need
DROP TABLE IF EXISTS public.role_permissions;
DROP TABLE IF EXISTS public.provider_role_permissions;

-- Step 5: Drop the app_permission enum
-- We no longer need granular permissions
DROP TYPE IF EXISTS public.app_permission;

-- Step 6: Restore user_roles data from backup
-- This ensures no user data is lost during the migration
DELETE FROM public.user_roles;
INSERT INTO public.user_roles (user_id, role, provider_role, created_at)
SELECT user_id, role, provider_role, created_at 
FROM user_roles_backup;

-- Step 7: Create simplified RLS policies for profiles
-- These policies use simple role-based checks instead of permission-based checks

CREATE POLICY "profiles_select_policy"
  ON public.profiles FOR SELECT
  TO authenticated
  USING (
    -- Users can view their own profile
    id = (SELECT auth.uid()) 
    OR 
    -- Admins can view all profiles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  );

CREATE POLICY "profiles_update_policy"
  ON public.profiles FOR UPDATE
  TO authenticated
  USING (
    -- Users can update their own profile
    id = (SELECT auth.uid()) 
    OR 
    -- Admins can update all profiles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  );

-- Step 8: Create simplified RLS policies for user_roles
-- These policies control who can view and modify user roles

CREATE POLICY "user_roles_select_policy"
  ON public.user_roles FOR SELECT
  TO authenticated
  USING (
    -- Users can view their own roles
    user_id = (SELECT auth.uid()) 
    OR 
    -- Admins can view all user roles
    EXISTS (
      SELECT 1 FROM public.user_roles ur 
      WHERE ur.user_id = (SELECT auth.uid()) 
      AND ur.role = 'admin'
    )
  );

CREATE POLICY "user_roles_insert_policy"
  ON public.user_roles FOR INSERT
  TO authenticated
  WITH CHECK (
    -- Only admins can assign roles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  );

CREATE POLICY "user_roles_update_policy"
  ON public.user_roles FOR UPDATE
  TO authenticated
  USING (
    -- Only admins can modify roles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  )
  WITH CHECK (
    -- Only admins can modify roles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  );

CREATE POLICY "user_roles_delete_policy"
  ON public.user_roles FOR DELETE
  TO authenticated
  USING (
    -- Only admins can delete roles
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = (SELECT auth.uid()) 
      AND role = 'admin'
    )
  );

-- Step 9: Create helper functions for role checking
-- These replace the complex authorize() function with simple role checks

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = auth.uid() 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = auth.uid() 
    AND role = 'catering_provider'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.is_provider_owner()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_id = auth.uid() 
    AND role = 'catering_provider'
    AND provider_role = 'owner'
  );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

CREATE OR REPLACE FUNCTION public.get_user_role()
RETURNS app_role AS $$
DECLARE
  user_role app_role;
BEGIN
  SELECT role INTO user_role
  FROM public.user_roles
  WHERE user_id = auth.uid()
  LIMIT 1;

  RETURN COALESCE(user_role, 'user');
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- Step 10: Update the custom access token hook to remove permissions
-- This simplifies the JWT claims to only include role information

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  claims jsonb;
  user_role public.app_role;
  user_provider_role public.provider_role_type;
BEGIN
  -- Get user role information
  SELECT ur.role, ur.provider_role
  INTO user_role, user_provider_role
  FROM public.user_roles ur
  WHERE ur.user_id = (event->>'user_id')::uuid
  LIMIT 1;

  -- Set default role if none found
  user_role := COALESCE(user_role, 'user');

  -- Build simplified claims with only role information
  claims := jsonb_build_object(
    'user_role', user_role,
    'provider_role', user_provider_role
  );

  -- Return the event with updated claims
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$;

-- Step 11: Create indexes for performance
-- These indexes optimize the simplified role-based queries

CREATE INDEX IF NOT EXISTS idx_user_roles_user_id_role ON public.user_roles (user_id, role);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON public.user_roles (role);
CREATE INDEX IF NOT EXISTS idx_user_roles_provider_role ON public.user_roles (provider_role) WHERE provider_role IS NOT NULL;

-- Step 12: Clean up temporary backup table
DROP TABLE user_roles_backup;

-- Step 13: Add comments for documentation
COMMENT ON FUNCTION public.is_admin() IS 'Returns true if the current user has admin role';
COMMENT ON FUNCTION public.is_provider() IS 'Returns true if the current user has catering_provider role';
COMMENT ON FUNCTION public.is_provider_owner() IS 'Returns true if the current user is a catering provider owner';
COMMENT ON FUNCTION public.get_user_role() IS 'Returns the current user''s role, defaulting to user if none found';
COMMENT ON FUNCTION public.custom_access_token_hook(jsonb) IS 'Simplified JWT hook that only includes role information in claims';
